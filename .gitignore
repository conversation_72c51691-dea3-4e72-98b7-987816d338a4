vite.config.*.timestamp*
vitest.config.*.timestamp*


# Node / Dependencies
node_modules/

# Third-party libraries
uihub-core-main/
icollect-web-main/
dist/
tmp/
coverage/
.nx/cache

# IDE / OS specific
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Angular cache
.angular/cache/

# Environment files
.env
.env.local
.env.*.local

# Nx
/apps/*/.vite
/apps/*/.angular
/apps/*/dist
/apps/*/tsconfig.tsbuildinfo
/dist/
/nx.json
*.tsbuildinfo