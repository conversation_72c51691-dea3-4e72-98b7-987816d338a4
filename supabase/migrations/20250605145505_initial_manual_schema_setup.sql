-- Declarative schema based on bbo-schema.txt

-- Create Tables
CREATE TABLE public.brokerage_firm (
    brokerage_id text PRIMARY KEY,
    firm_name text,
    firm_code text UNIQUE,
    license_number text,
    regulatory_id text,
    clearing_firm text,
    contact_info text,
    address text,
    city text,
    state text,
    country text,
    is_active boolean,
    subscription_tier text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

CREATE TABLE public.users ( -- Renamed from USER to avoid keyword conflict
    user_id text PRIMARY KEY,
    supabase_user_id uuid UNIQUE,
    brokerage_id text,
    email text,
    user_type text,
    role text,
    permissions jsonb,
    first_name text,
    last_name text,
    phone text,
    is_active boolean,
    last_login_at timestamptz,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

CREATE TABLE public.user_brokerage_access (
    access_id text PRIMARY KEY,
    user_id text,
    brokerage_id text,
    role text,
    permissions jsonb,
    granted_by text, -- Assuming this refers to a user_id
    granted_at timestamptz,
    revoked_at timestamptz,
    is_active boolean
);

CREATE TABLE public.client (
    client_id text PRIMARY KEY,
    brokerage_id text,
    first_name text,
    last_name text,
    email text,
    phone text,
    date_of_birth date,
    ssn text, -- Consider encryption for sensitive data
    address text,
    city text,
    state text,
    zip_code text,
    country text,
    account_status text,
    kyc_status text,
    risk_profile text,
    net_worth numeric,
    annual_income numeric,
    investment_experience text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

CREATE TABLE public.account (
    account_id text PRIMARY KEY,
    brokerage_id text,
    client_id text,
    account_number text,
    account_type text,
    account_status text,
    base_currency text,
    opening_balance numeric,
    current_balance numeric,
    available_balance numeric,
    margin_balance numeric,
    buying_power numeric,
    maintenance_margin numeric,
    day_trading_buying_power numeric,
    opened_date date,
    closed_date date,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

CREATE TABLE public.portfolio (
    portfolio_id text PRIMARY KEY,
    brokerage_id text,
    account_id text,
    portfolio_name text,
    portfolio_type text,
    total_value numeric,
    cash_balance numeric,
    invested_value numeric,
    unrealized_pnl numeric,
    realized_pnl numeric,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

CREATE TABLE public.security (
    security_id text PRIMARY KEY,
    symbol text UNIQUE,
    cusip text,
    isin text,
    security_name text,
    security_type text,
    exchange text,
    sector text,
    industry text,
    country text,
    currency text,
    is_active boolean,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

CREATE TABLE public.position (
    position_id text PRIMARY KEY,
    brokerage_id text,
    portfolio_id text,
    security_id text,
    quantity numeric,
    avg_cost numeric,
    market_value numeric,
    unrealized_pnl numeric,
    realized_pnl numeric,
    position_type text,
    last_updated timestamptz,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE public.orders ( -- Renamed from ORDER to avoid keyword conflict
    order_id text PRIMARY KEY,
    brokerage_id text,
    account_id text,
    security_id text,
    order_number text,
    order_type text,
    order_side text,
    order_status text,
    quantity numeric,
    price numeric,
    stop_price numeric,
    time_in_force text,
    order_date timestamptz,
    expiry_date timestamptz,
    filled_quantity numeric,
    avg_fill_price numeric,
    commission numeric,
    fees numeric,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

CREATE TABLE public.trade (
    trade_id text PRIMARY KEY,
    brokerage_id text,
    order_id text,
    position_id text,
    broker_id text, -- Assuming this refers to a user_id
    trade_number text,
    trade_side text,
    quantity numeric,
    price numeric,
    trade_value numeric,
    commission numeric,
    fees numeric,
    settlement_date date,
    trade_date timestamptz,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE public.transaction (
    transaction_id text PRIMARY KEY,
    brokerage_id text,
    account_id text,
    trade_id text, -- This can be NULL if transaction is not from a trade
    transaction_type text,
    amount numeric,
    currency text,
    description text,
    reference_number text,
    status text,
    transaction_date timestamptz,
    settlement_date date,
    created_at timestamptz DEFAULT now()
);

-- Enable Row Level Security for all tables
ALTER TABLE public.brokerage_firm ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_brokerage_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.account ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.portfolio ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.security ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.position ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trade ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transaction ENABLE ROW LEVEL SECURITY;

-- Add Foreign Key Constraints
ALTER TABLE public.users ADD CONSTRAINT fk_users_brokerage_id FOREIGN KEY (brokerage_id) REFERENCES public.brokerage_firm(brokerage_id);
ALTER TABLE public.users ADD CONSTRAINT fk_users_supabase_user_id FOREIGN KEY (supabase_user_id) REFERENCES auth.users(id);

ALTER TABLE public.user_brokerage_access ADD CONSTRAINT fk_user_brokerage_access_user_id FOREIGN KEY (user_id) REFERENCES public.users(user_id);
ALTER TABLE public.user_brokerage_access ADD CONSTRAINT fk_user_brokerage_access_brokerage_id FOREIGN KEY (brokerage_id) REFERENCES public.brokerage_firm(brokerage_id);
ALTER TABLE public.user_brokerage_access ADD CONSTRAINT fk_user_brokerage_access_granted_by FOREIGN KEY (granted_by) REFERENCES public.users(user_id);

ALTER TABLE public.client ADD CONSTRAINT fk_client_brokerage_id FOREIGN KEY (brokerage_id) REFERENCES public.brokerage_firm(brokerage_id);

ALTER TABLE public.account ADD CONSTRAINT fk_account_brokerage_id FOREIGN KEY (brokerage_id) REFERENCES public.brokerage_firm(brokerage_id);
ALTER TABLE public.account ADD CONSTRAINT fk_account_client_id FOREIGN KEY (client_id) REFERENCES public.client(client_id);

ALTER TABLE public.portfolio ADD CONSTRAINT fk_portfolio_brokerage_id FOREIGN KEY (brokerage_id) REFERENCES public.brokerage_firm(brokerage_id);
ALTER TABLE public.portfolio ADD CONSTRAINT fk_portfolio_account_id FOREIGN KEY (account_id) REFERENCES public.account(account_id);

ALTER TABLE public.position ADD CONSTRAINT fk_position_brokerage_id FOREIGN KEY (brokerage_id) REFERENCES public.brokerage_firm(brokerage_id);
ALTER TABLE public.position ADD CONSTRAINT fk_position_portfolio_id FOREIGN KEY (portfolio_id) REFERENCES public.portfolio(portfolio_id);
ALTER TABLE public.position ADD CONSTRAINT fk_position_security_id FOREIGN KEY (security_id) REFERENCES public.security(security_id);

ALTER TABLE public.orders ADD CONSTRAINT fk_orders_brokerage_id FOREIGN KEY (brokerage_id) REFERENCES public.brokerage_firm(brokerage_id);
ALTER TABLE public.orders ADD CONSTRAINT fk_orders_account_id FOREIGN KEY (account_id) REFERENCES public.account(account_id);
ALTER TABLE public.orders ADD CONSTRAINT fk_orders_security_id FOREIGN KEY (security_id) REFERENCES public.security(security_id);

ALTER TABLE public.trade ADD CONSTRAINT fk_trade_brokerage_id FOREIGN KEY (brokerage_id) REFERENCES public.brokerage_firm(brokerage_id);
ALTER TABLE public.trade ADD CONSTRAINT fk_trade_order_id FOREIGN KEY (order_id) REFERENCES public.orders(order_id);
ALTER TABLE public.trade ADD CONSTRAINT fk_trade_position_id FOREIGN KEY (position_id) REFERENCES public.position(position_id);
ALTER TABLE public.trade ADD CONSTRAINT fk_trade_broker_id FOREIGN KEY (broker_id) REFERENCES public.users(user_id);

ALTER TABLE public.transaction ADD CONSTRAINT fk_transaction_brokerage_id FOREIGN KEY (brokerage_id) REFERENCES public.brokerage_firm(brokerage_id);
ALTER TABLE public.transaction ADD CONSTRAINT fk_transaction_account_id FOREIGN KEY (account_id) REFERENCES public.account(account_id);
ALTER TABLE public.transaction ADD CONSTRAINT fk_transaction_trade_id FOREIGN KEY (trade_id) REFERENCES public.trade(trade_id) ON DELETE SET NULL; -- Assuming trade_id can be optional
