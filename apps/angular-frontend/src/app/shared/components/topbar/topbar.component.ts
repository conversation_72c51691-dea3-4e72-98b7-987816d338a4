import { Component, AfterViewInit } from '@angular/core';

declare const initUIcons: Function; // Declare initUIcons as a global function

@Component({
  selector: 'app-topbar',
  standalone: true, // Assuming standalone
  imports: [],
  templateUrl: './topbar.component.html',
  styleUrls: ['./topbar.component.css']
})
export class TopbarComponent implements AfterViewInit {

  logout(): void {
    // TODO: Implement actual logout logic (e.g., call auth service, navigate to login)
    console.log('Logout clicked');
    alert('Logout functionality to be implemented.');
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      if (typeof initUIcons === 'function') {
        initUIcons();
        console.log('TopbarComponent: initUIcons called after timeout.'); // Added for debugging
      } else {
        console.error('TopbarComponent: initUIcons function not found after timeout.');
      }
    }, 0); // 0ms timeout, defers execution until after current call stack
  }
}
