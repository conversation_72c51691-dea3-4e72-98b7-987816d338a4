<nav class="navbar sticky">
    <ul>
        <li class="no-separator">
            <a href="javascript:void(0);">
                <div class="badge-container">
                    <span class="u-icon u-icon-24 text-grey-normal">o-bell</span>
                    <span class="badge badge-small" style="--margin-left: -10px; --margin-top: 0px;">0</span> <!-- Changed +99 to 0 -->
                </div>
            </a>
            <ul>
                <li class="border-bottom">
                    <div class="text-center flex flex-center gap-2">
                        <span class="u-icon u-icon-24 text-primary-normal">bell</span>
                        <p>Notifications</p>
                        <span class="badge">0</span> <!-- Changed 99+ to 0 -->
                    </div>
                </li>
                <li>
                    <div class="p-3 text-center">
                        <p class="text-grey-normal">No new notifications</p>
                    </div>
                </li>
                <!-- Example notification items - remove or adapt
                <li>
                    <div>
                        <p class="text-bold mb-1 text-grey-dark">Activity</p>
                        <p class="text-grey-normal">30 mins ago</p>
                    </div>
                </li>
                -->
                <li class="border-bottom"></li>
                <li>
                    <a href="#" class="text-bold text-center m-3 text-primary-normal">See all Activities</a>
                </li>
            </ul>
        </li>
        <li><span>Brokerage Firm XYZ</span></li> <!-- Placeholder -->
        <li><span>User ID</span></li> <!-- Placeholder -->
        <li>
            <a href="javascript:void(0);" class="flex gap-4px">
                <span class="u-icon u-icon-24 text-primary-normal">user</span>
                <span class="u-icon text-primary-normal">o-chevron-down</span>
            </a>
            <ul>
                <li class="border-bottom">
                    <div data-layout="col: max-content 1fr" class="gap-1 align-center">
                        <span class="u-icon u-icon-48 text-primary-normal">user</span>
                        <div>
                            <span class="text-bold">User Name</span> <!-- Placeholder -->
                            <p>User Role</p> <!-- Placeholder -->
                        </div>
                    </div>
                </li>
                <li>
                    <div data-layout="col: max-content 1fr" class="gap-3 align-center pointer" (click)="logout()">
                        <span class="u-icon u-icon-24 text-error-normal">o-sign-out</span>
                        <span class="text-bold text-error-normal">Logout</span>
                    </div>
                </li>
            </ul>
        </li>
    </ul>
</nav>
