<div class="stepper-container width-max">
    <div class="progressbar">
        @for (step of steps; track step; let i = $index) {
        <div class="step-item" [class.active]="i === activeIndex" [class.done]="i < activeIndex">
            <div class="progress-count">
                <span>{{ i + 1 }}</span>
            </div>
            <!-- The step.content is derived from the textContent of the projected element -->
            <!-- If you want more structured content, you might need to adjust StepDirective and updateSteps logic -->
            <p class="progress-label">{{ step.title }}</p> 
        </div>
        }
    </div>
</div>
