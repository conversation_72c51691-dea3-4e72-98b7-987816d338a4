import { AfterContentInit, Component, ContentChildren, Input, OnChanges, QueryList, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StepDirective } from './step.directive';

@Component({
  selector: 'app-steps',
  standalone: true,
  imports: [CommonModule], // Removed StepDirective from imports
  templateUrl: './steps.component.html',
})

export class StepsComponent implements OnChanges, AfterContentInit {
  @Input() active = 0;
  @ContentChildren(StepDirective) stepElements!: QueryList<StepDirective>;
  
  activeIndex = 0;
  steps: { title: string, content: string }[] = [];
  
  ngAfterContentInit() {
    this.updateSteps();
    
    // Listen for changes in the content children
    this.stepElements.changes.subscribe(() => {
      this.updateSteps();
    });
  }
  
  ngOnChanges(changes: SimpleChanges) {
    if (changes['active']) {
      // Convert to number
      this.activeIndex = Number(this.active);
      
      // Ensure activeIndex is within valid range
      if (this.activeIndex < 0) {
        this.activeIndex = 0;
      }
      // Ensure activeIndex is not out of bounds if steps are already populated
      if (this.steps && this.steps.length > 0 && this.activeIndex >= this.steps.length) {
        this.activeIndex = this.steps.length - 1;
      }
    }
  }
  
  private updateSteps() {
    if (this.stepElements) {
      this.steps = this.stepElements.map(step => ({
        title: step.title,
        // Accessing the projected content for the step's label/content
        // This might need adjustment based on how you structure content within <x-step>
        content: step.element.nativeElement.textContent?.trim() || '' 
      }));
       // Re-validate activeIndex after steps are updated
      if (this.activeIndex >= this.steps.length && this.steps.length > 0) {
        this.activeIndex = this.steps.length - 1;
      }
      if (this.activeIndex < 0 && this.steps.length > 0) {
        this.activeIndex = 0;
      }
    }
  }
}
