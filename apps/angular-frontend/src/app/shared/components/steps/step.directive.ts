import { Directive, ElementRef, Input } from '@angular/core';

@Directive({
  selector: '[appStep]', // Changed selector to be more conventional for attribute directives
  standalone: true
})
export class StepDirective {
  @Input('appStep') title = ''; // Bind to the directive attribute itself for title
  // @Input() content: string = ''; // Optional: if you want to pass content via an input too
  
  constructor(public element: ElementRef) {}
}
