<aside>
    <div class="sidebar">
        <section class="flex flex-center">
            <!-- TODO: Adjust path to logo if needed for gp_brokerage_backoffice -->
            <img src="assets/images/default-logo.png" alt="Brokerage Backoffice" class="text-center">
        </section>
        <section data-navigation>
            <br>
            <header class="text-muted">NAVIGATION</header>
            <ul>
                <li tabindex="0">
                    <!-- TODO: Update routerLink for gp_brokerage_backoffice -->
                    <a routerLink="/onboard-brokerage" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" class="flex">Onboard Brokerage</a>
                </li>
                <!-- Add other relevant links for gp_brokerage_backoffice -->
                <!-- Example from icollect-web-main:
                <li tabindex="0">
                    <a routerLink="/reports" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" class="flex">Reports</a>
                </li>
                <li tabindex="0">
                    <a routerLink="/transactions-till" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" class="flex">Teller's Till</a>
                </li>
                -->
            </ul>
            <!-- <br><br>
            <header class="text-muted">MANAGEMENT</header>
            <ul>
                <li tabindex="0">
                    <a routerLink="/merchants-management" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" class="flex">Merchants Management</a>
                </li>
            </ul> -->
            <br><br>
            <header class="text-muted">SUPPORT</header>
            <ul>
                <li tabindex="0">
                    <a href="#" class="flex">Settings</a>
                </li>
                <li tabindex="0">
                    <a href="#" class="flex">Manuals</a>
                </li>
                <li tabindex="0">
                    <a href="#" class="flex">FAQ</a>
                </li>
            </ul>
        </section>
        <section class="p-3">
            <span class="text-small">(Version {{appVersion}}) - {{appEnvironment}}</span>
        </section>
    </div>
</aside>
