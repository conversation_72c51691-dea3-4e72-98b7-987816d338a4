import { Component, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { RouterLink, RouterModule } from '@angular/router';

@Component({
  selector: 'app-sidebar',
  standalone: true, // Assuming standalone based on common practice for new components
  imports: [RouterLink, RouterModule],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'] // Corrected styleUrl to styleUrls
})
export class SidebarComponent implements OnInit {
  appVersion = '--.--';
  appEnvironment = '--';

  constructor(@Inject(PLATFORM_ID) private platformId: object) { }

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      fetch('assets/version.json') // Assuming version.json will be in assets
        .then(response => response.json())
        .then(data => {
          this.appVersion = data.version;
          this.appEnvironment = data.environment;
        })
        .catch(error => {
          console.error('Error fetching version data:', error);
          // Provide default values or handle more gracefully
          this.appVersion = 'N/A';
          this.appEnvironment = 'N/A';
        });
    } else {
      // Optionally set default values for SSR or leave as is if client fetch is guaranteed
      this.appVersion = 'N/A';
      this.appEnvironment = 'SSR'; // Placeholder for SSR
    }
  }
}
