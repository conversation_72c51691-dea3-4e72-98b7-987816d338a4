import { Component, AfterViewInit } from '@angular/core';

declare const UIhub: any; // Declare UIhub as a global variable
import { RouterModule } from '@angular/router';
import { SidebarComponent } from './shared/components/sidebar/sidebar.component';
import { TopbarComponent } from './shared/components/topbar/topbar.component';


@Component({
  imports: [RouterModule, SidebarComponent, TopbarComponent],
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.css',
})
export class AppComponent implements AfterViewInit {
  title = 'angular-frontend';

  ngAfterViewInit(): void {
    if (typeof UIhub !== 'undefined' && UIhub.init) {
      UIhub.init();
      console.log('UI Hub Initialized');
    } else {
      console.error('UI Hub not found or init method is missing.');
    }
  }
}
