/* Styles for Brokerage Onboarding Component */

/* Input field styling */
.bordered-box {
  border: 1px solid #D0D5DD; /* Adjusted for a common modern look, similar to #70707070 but slightly different shade */
  border-radius: 4px;
  background-color: #FFFFFF; /* Assuming white background for inputs */
  height: 48px;
  padding: 0 12px; /* Added padding for text inside input */
  font-size: 1rem; /* Ensure consistent font size */
  color: #333333; /* Darker text for readability */
  width: 100%; /* Make input take full width of its container */
  box-sizing: border-box; /* Include padding and border in the element's total width and height */
}

.bordered-box:focus {
  outline: none;
  border-color: #007bff; /* Highlight focus with primary blue */
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25); /* Focus shadow */
}


/* Stepper button container styling */
.stepper-buttons {
  display: flex;
  flex-direction: row;
  gap: 16px;
  width: 300px;
  margin-left: auto;
  margin-top: 60px;
}

.stepper-buttons button {
  flex: 1;
}

/* Target UI Hub buttons within our stepper for specific overrides */
.stepper-buttons button.outlined {
  border-color: #007bff; /* Primary blue */
  color: #007bff;       /* Primary blue */
  background-color: #FFFFFF; /* Ensure white background */
}

/* Address potential padding differences */
.stepper-buttons button.outlined,
.stepper-buttons button.ml-auto /* Catches Next/Submit as they only have ml-auto */
{
  padding-left: 20px;
  padding-right: 20px;
}

/* Ensure disabled outlined button is appropriately styled */
.stepper-buttons button.outlined:disabled {
  border-color: #D0D5DD; /* Muted border */
  color: #A0A0A0;       /* Muted text */
  background-color: #F8F9FA; /* Slightly off-white background */
}

/* Ensure labels have consistent styling */
.label {
  color: #4A4949;
  margin-bottom: 8px; /* Add some space below the label */
  font-weight: 500; /* Medium font weight for labels */
  display: block; /* Make label take full width */
  font-size: 0.9rem;
}

/* Form field container */
.form-field {
  margin-bottom: 1rem; /* Add space between form fields */
}
