<div class="p-horizontal-large flex-column gap-2">
  <h2 class="text-xlarge text-bold">Onboard New Brokerage Firm</h2>

  <app-steps [active]="currentStep">
    <div *appStep="'General Info'" data-step-content="Step 1: Provide general firm details.">
      <!-- Content for Step 1: General Info -->
    </div>
    <div *appStep="'Contact Info'" data-step-content="Step 2: Enter contact information.">
      <!-- Content for Step 2: Contact Info -->
    </div>
    <div *appStep="'Billing Config'" data-step-content="Step 3: Configure billing and subscription.">
      <!-- Content for Step 3: Billing Configuration -->
    </div>
  </app-steps>

  <!-- Form Content Area: Switches based on currentStep -->
  <div [ngSwitch]="currentStep">
    <!-- STEP 1: General Info -->
    <div *ngSwitchCase="0">
      <form [formGroup]="generalInfoForm" class="flex-column gap-1">
        <div class="form-field">
          <label class="label" for="firm_name">Firm Name</label>
          <input type="text" id="firm_name" formControlName="firm_name" class="bordered-box">
          <div *ngIf="generalInfoForm.get('firm_name')?.invalid && (generalInfoForm.get('firm_name')?.dirty || generalInfoForm.get('firm_name')?.touched)" class="text-error-normal text-small mt-1">
            <div *ngIf="generalInfoForm.get('firm_name')?.errors?.['required']">Firm Name is required.</div>
          </div>
        </div>

        <div class="form-field">
          <label class="label" for="firm_code">Firm Code</label>
          <input type="text" id="firm_code" formControlName="firm_code" class="bordered-box">
          <div *ngIf="generalInfoForm.get('firm_code')?.invalid && (generalInfoForm.get('firm_code')?.dirty || generalInfoForm.get('firm_code')?.touched)" class="text-error-normal text-small mt-1">
            <div *ngIf="generalInfoForm.get('firm_code')?.errors?.['required']">Firm Code is required.</div>
          </div>
        </div>
        
        <div class="form-field">
          <label class="label" for="license_number">License Number</label>
          <input type="text" id="license_number" formControlName="license_number" class="bordered-box">
        </div>

        <div class="form-field">
          <label class="label" for="regulatory_id">Regulatory ID</label>
          <input type="text" id="regulatory_id" formControlName="regulatory_id" class="bordered-box">
        </div>

        <div class="form-field">
          <label class="label" for="clearing_firm">Clearing Firm</label>
          <input type="text" id="clearing_firm" formControlName="clearing_firm" class="bordered-box">
        </div>
      </form>
    </div>

    <!-- STEP 2: Contact Info -->
    <div *ngSwitchCase="1">
      <form [formGroup]="contactInfoForm" class="flex-column gap-1">
        <div class="form-field">
          <label class="label" for="contact_info">Contact Info (Details)</label>
          <textarea id="contact_info" formControlName="contact_info" class="bordered-box" rows="3"></textarea>
        </div>

        <div class="form-field">
          <label class="label" for="address">Address</label>
          <input type="text" id="address" formControlName="address" class="bordered-box">
        </div>

        <div class="form-field">
          <label class="label" for="city">City</label>
          <input type="text" id="city" formControlName="city" class="bordered-box">
        </div>

        <div class="form-field">
          <label class="label" for="state">State</label>
          <input type="text" id="state" formControlName="state" class="bordered-box">
        </div>

        <div class="form-field">
          <label class="label" for="country">Country</label>
          <input type="text" id="country" formControlName="country" class="bordered-box">
          <div *ngIf="contactInfoForm.get('country')?.invalid && (contactInfoForm.get('country')?.dirty || contactInfoForm.get('country')?.touched)" class="text-error-normal text-small mt-1">
            <div *ngIf="contactInfoForm.get('country')?.errors?.['required']">Country is required.</div>
          </div>
        </div>
      </form>
    </div>

    <!-- STEP 3: Billing Configuration -->
    <div *ngSwitchCase="2">
      <form [formGroup]="billingConfigForm" class="flex-column gap-1">
        <div class="form-field">
          <label class="label" for="subscription_tier">Subscription Tier</label>
          <select id="subscription_tier" formControlName="subscription_tier" class="bordered-box">
            <option value="basic">Basic</option>
            <option value="premium">Premium</option>
            <option value="enterprise">Enterprise</option>
          </select>
          <div *ngIf="billingConfigForm.get('subscription_tier')?.invalid && (billingConfigForm.get('subscription_tier')?.dirty || billingConfigForm.get('subscription_tier')?.touched)" class="text-error-normal text-small mt-1">
            <div *ngIf="billingConfigForm.get('subscription_tier')?.errors?.['required']">Subscription Tier is required.</div>
          </div>
        </div>

        <div class="form-field flex-row align-center gap-1 mt-1">
          <input type="checkbox" id="is_active" formControlName="is_active" class="checkbox-input">
          <label for="is_active" class="label">Is Active</label>
        </div>
      </form>
    </div>
  </div>

  <!-- Stepper Buttons -->
  <div class="stepper-buttons flex-row gap-1 mt-2">
    <button type="button" class="outlined" (click)="prevStep()" [disabled]="currentStep === 0">
      Previous
    </button>
    <button type="button" (click)="nextStep()" *ngIf="currentStep < stepTitles.length - 1">
      Next
    </button>
    <button type="button" (click)="onSubmit()" *ngIf="currentStep === stepTitles.length - 1">
      Submit Onboarding
    </button>
  </div>

</div>
