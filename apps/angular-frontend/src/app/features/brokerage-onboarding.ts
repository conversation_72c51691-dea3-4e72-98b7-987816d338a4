import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { StepsComponent } from '../shared/components/steps/steps.component';
import { StepDirective } from '../shared/components/steps/step.directive';

@Component({
  selector: 'app-brokerage-onboarding',
  imports: [CommonModule, ReactiveFormsModule, StepsComponent, StepDirective],
  templateUrl: './brokerage-onboarding.html',
  styleUrl: './brokerage-onboarding.css',
})
export class BrokerageOnboardingComponent implements OnInit {
  currentStep = 0;
  generalInfoForm!: FormGroup;
  contactInfoForm!: FormGroup;
  billingConfigForm!: FormGroup;
  stepTitles: string[] = ['General Info', 'Contact Info', 'Billing Config'];

  // Store for easy access in template
  formSteps!: FormGroup[];

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.generalInfoForm = this.fb.group({
      firm_name: ['', Validators.required],
      firm_code: ['', Validators.required],
      license_number: [''],
      regulatory_id: [''],
      clearing_firm: ['']
    });

    this.contactInfoForm = this.fb.group({
      contact_info: [''], // Will be a textarea
      address: [''],
      city: [''],
      state: [''],
      country: ['', Validators.required]
    });

    this.billingConfigForm = this.fb.group({
      subscription_tier: ['basic', Validators.required],
      is_active: [true]
    });

    this.formSteps = [this.generalInfoForm, this.contactInfoForm, this.billingConfigForm];
  }

  get currentFormGroup(): FormGroup {
    return this.formSteps[this.currentStep];
  }

  nextStep(): void {
    if (this.currentFormGroup.valid) {
      if (this.currentStep < this.formSteps.length - 1) {
        this.currentStep++;
      }
    } else {
      this.currentFormGroup.markAllAsTouched();
      console.error('Current step form is invalid');
    }
  }

  prevStep(): void {
    if (this.currentStep > 0) {
      this.currentStep--;
    }
  }

  onSubmit(): void {
    // Validate all forms before final submission, or ensure all steps were completed and validated.
    // For simplicity here, we'll check the last one, assuming navigation enforced prior validation.
    if (this.billingConfigForm.valid && this.generalInfoForm.valid && this.contactInfoForm.valid) {
      const brokerageData = {
        ...this.generalInfoForm.value,
        ...this.contactInfoForm.value,
        ...this.billingConfigForm.value
      };
      console.log('Brokerage Firm Data:', brokerageData);
      // Here you would typically send the data to a service
      alert('Brokerage Onboarding Submitted Successfully!'); // Placeholder for success
    } else {
      console.error('One or more steps are invalid. Please review your entries.');
      // Optionally, navigate to the first invalid step or mark all fields in all invalid forms as touched.
      this.formSteps.forEach(form => {
        if (!form.valid) {
          form.markAllAsTouched();
        }
      });
    }
  }
}
