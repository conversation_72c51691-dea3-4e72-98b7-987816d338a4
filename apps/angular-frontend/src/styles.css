/* Global styles from icollect-web-main project */
:root {
  --primary-darker: #000d22;
}

.layout {
  --aside-width: 20rem;
}

.flex-row{
  display: flex;
  flex-direction: row;
}

.flex-column{
  display: flex;
  flex-direction: column;
}

/* Overrides */
x-steps {
  width: max-content;
  display: block;
}

details ul li {
  margin-left: 0;
}

input[type=text].input-error {
  border: 1px solid var(--error-normal) !important;
}

.small-table .data-table > table thead > tr > th {
  padding-block: .9rem !important;
}

/* Utility classes */
.mt-4 {
  margin-top: 2rem;
}

.bg-transparent input,
.bg-transparent {
    background-color: transparent;
}
.ml-auto {
  margin-left: auto;
}

.p-horizontal-large {
    padding-inline: 5rem;

    @media screen and (max-width: 900px) {
        padding-inline: 1rem;
    }

    @media screen and (min-width: 900px) and (max-width: 1200px) {
        padding-inline: 1rem;
    }
}

.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-auto {
  overflow-x: auto;
}

.min-height-100 {
  min-height: 100%;
}

.width-max {
  width: max-content;
}

.width-500px {
  width: min(500px, 100%);

  /* @media screen and (max-width: 768px) {
    width: 100%;
  } */
}

.width-600px {
  width: min(600px, 100%);

  /* @media screen and (max-width: 768px) {
    width: 100%;
  } */
}

.width-60 {
  width: 60%;

  @media screen and (max-width: 768px) {
    width: 100%;
  }
}

.width-70 {
  width: 70%;

  @media screen and (max-width: 1500px) {
    width: 100%;
  }
}

.width-80 {
  width: 80%;

  @media screen and (max-width: 768px) {
    width: 100%;
  }
}
