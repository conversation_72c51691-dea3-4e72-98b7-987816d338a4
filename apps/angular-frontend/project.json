{"name": "angular-frontend", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/angular-frontend/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/angular-frontend", "index": "apps/angular-frontend/src/index.html", "browser": "apps/angular-frontend/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/angular-frontend/tsconfig.app.json", "assets": ["apps/angular-frontend/src/favicon.ico", {"glob": "**/*", "input": "apps/angular-frontend/public"}, "apps/angular-frontend/src/assets"], "styles": ["apps/angular-frontend/src/styles.css"], "scripts": [], "server": "apps/angular-frontend/src/main.server.ts", "ssr": {"entry": "apps/angular-frontend/src/server.ts"}, "prerender": true}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "8kb", "maximumError": "12kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"continuous": true, "executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "angular-frontend:build:production"}, "development": {"buildTarget": "angular-frontend:build:development", "poll": 1000}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "angular-frontend:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"reportsDirectory": "../../coverage/apps/angular-frontend"}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "angular-frontend:build", "port": 4200, "staticFilePath": "dist/apps/angular-frontend/browser", "spa": true}}}}