{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "types": ["node"]}, "files": ["src/main.ts", "src/main.server.ts", "src/server.ts"], "include": ["src/**/*.d.ts"], "exclude": ["jest.config.ts", "src/**/*.test.ts", "src/**/*.spec.ts", "vite.config.ts", "vite.config.mts", "vitest.config.ts", "vitest.config.mts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx", "src/test-setup.ts"]}