// Capital Markets Brokerage Back Office - Multi-Tenant ERD
// Using Crow's Feet notation

notation crows-feet

brokerage_firm [icon: building, color: navy] {
  brokerage_id string pk
  firm_name string
  firm_code string unique
  license_number string
  regulatory_id string
  clearing_firm string
  contact_info string
  address string
  city string
  state string
  country string
  is_active boolean
  subscription_tier string
  created_at timestamp
  updated_at timestamp
}

users [icon: user-circle, color: emerald] {
  user_id string pk
  supabase_user_id uuid unique
  brokerage_id string
  email string
  user_type string
  role string
  permissions json
  first_name string
  last_name string
  phone string
  is_active boolean
  last_login_at timestamp
  created_at timestamp
  updated_at timestamp
}

user_brokerage_access [icon: key, color: gold] {
  access_id string pk
  user_id string
  brokerage_id string
  role string
  permissions json
  granted_by string
  granted_at timestamp
  revoked_at timestamp
  is_active boolean
}

client [icon: user, color: blue] {
  client_id string pk
  brokerage_id string
  first_name string
  last_name string
  email string
  phone string
  date_of_birth string
  ssn string
  address string
  city string
  state string
  zip_code string
  country string
  account_status string
  kyc_status string
  risk_profile string
  net_worth decimal
  annual_income decimal
  investment_experience string
  created_at timestamp
  updated_at timestamp
}

account [icon: credit-card, color: green] {
  account_id string pk
  brokerage_id string
  client_id string
  account_number string
  account_type string
  account_status string
  base_currency string
  opening_balance decimal
  current_balance decimal
  available_balance decimal
  margin_balance decimal
  buying_power decimal
  maintenance_margin decimal
  day_trading_buying_power decimal
  opened_date string
  closed_date string
  created_at timestamp
  updated_at timestamp
}

portfolio [icon: briefcase, color: purple] {
  portfolio_id string pk
  brokerage_id string
  account_id string
  portfolio_name string
  portfolio_type string
  total_value decimal
  cash_balance decimal
  invested_value decimal
  unrealized_pnl decimal
  realized_pnl decimal
  created_at timestamp
  updated_at timestamp
}

security [icon: chart-line, color: orange] {
  security_id string pk
  symbol string unique
  cusip string
  isin string
  security_name string
  security_type string
  exchange string
  sector string
  industry string
  country string
  currency string
  is_active boolean
  created_at timestamp
  updated_at timestamp
}

position [icon: target, color: red] {
  position_id string pk
  brokerage_id string
  portfolio_id string
  security_id string
  quantity decimal
  avg_cost decimal
  market_value decimal
  unrealized_pnl decimal
  realized_pnl decimal
  position_type string
  last_updated timestamp
  created_at timestamp
}

orders [icon: shopping-cart, color: yellow] {
  order_id string pk
  brokerage_id string
  account_id string
  security_id string
  order_number string
  order_type string
  order_side string
  order_status string
  quantity decimal
  price decimal
  stop_price decimal
  time_in_force string
  order_date timestamp
  expiry_date timestamp
  filled_quantity decimal
  avg_fill_price decimal
  commission decimal
  fees decimal
  created_at timestamp
  updated_at timestamp
}

trade [icon: exchange-alt, color: teal] {
  trade_id string pk
  brokerage_id string
  order_id string
  position_id string
  broker_id string
  trade_number string
  trade_side string
  quantity decimal
  price decimal
  trade_value decimal
  commission decimal
  fees decimal
  settlement_date string
  trade_date timestamp
  created_at timestamp
}

transaction [icon: dollar-sign, color: indigo] {
  transaction_id string pk
  brokerage_id string
  account_id string
  trade_id string
  transaction_type string
  amount decimal
  currency string
  description string
  reference_number string
  status string
  transaction_date timestamp
  settlement_date string
  created_at timestamp
}

MARKET_DATA [icon: chart-bar, color: cyan] {
  market_data_id string pk
  security_id string
  bid_price decimal
  ask_price decimal
  last_price decimal
  volume bigint
  high_price decimal
  low_price decimal
  open_price decimal
  close_price decimal
  change_percent decimal
  market_cap decimal
  timestamp timestamp
}

COMPLIANCE_RULE [icon: shield-alt, color: pink] {
  rule_id string pk
  brokerage_id string
  rule_name string
  rule_type string
  rule_description string
  parameters string
  is_active boolean
  created_at timestamp
  updated_at timestamp
}

COMPLIANCE_VIOLATION [icon: exclamation-triangle, color: red] {
  violation_id string pk
  brokerage_id string
  account_id string
  rule_id string
  violation_type string
  description string
  severity string
  status string
  detected_at timestamp
  resolved_at timestamp
  resolution_notes string
  created_at timestamp
}

BROKER [icon: handshake, color: brown] {
  broker_id string pk
  brokerage_id string
  broker_name string
  broker_code string
  contact_info string
  employee_id string
  is_active boolean
  created_at timestamp
}

SETTLEMENT [icon: calendar-check, color: gray] {
  settlement_id string pk
  brokerage_id string
  trade_id string
  account_id string
  settlement_date string
  net_amount decimal
  currency string
  status string
  clearing_firm string
  settlement_instructions string
  created_at timestamp
}

// Relationships using Eraser syntax
BROKERAGE_FIRM.brokerage_id < USER.brokerage_id
BROKERAGE_FIRM.brokerage_id < USER_BROKERAGE_ACCESS.brokerage_id
USER.user_id < USER_BROKERAGE_ACCESS.user_id

BROKERAGE_FIRM.brokerage_id < CLIENT.brokerage_id
BROKERAGE_FIRM.brokerage_id < ACCOUNT.brokerage_id
BROKERAGE_FIRM.brokerage_id < PORTFOLIO.brokerage_id
BROKERAGE_FIRM.brokerage_id < POSITION.brokerage_id
BROKERAGE_FIRM.brokerage_id < ORDER.brokerage_id
BROKERAGE_FIRM.brokerage_id < TRADE.brokerage_id
BROKERAGE_FIRM.brokerage_id < TRANSACTION.brokerage_id
BROKERAGE_FIRM.brokerage_id < COMPLIANCE_RULE.brokerage_id
BROKERAGE_FIRM.brokerage_id < COMPLIANCE_VIOLATION.brokerage_id
BROKERAGE_FIRM.brokerage_id < BROKER.brokerage_id
BROKERAGE_FIRM.brokerage_id < SETTLEMENT.brokerage_id

CLIENT.client_id < ACCOUNT.client_id
ACCOUNT.account_id < PORTFOLIO.account_id
PORTFOLIO.portfolio_id < POSITION.portfolio_id
POSITION.security_id > SECURITY.security_id
ACCOUNT.account_id < ORDER.account_id
ORDER.security_id > SECURITY.security_id
ORDER.order_id < TRADE.order_id
TRADE.position_id > POSITION.position_id
ACCOUNT.account_id < TRANSACTION.account_id
TRANSACTION.trade_id > TRADE.trade_id
SECURITY.security_id - MARKET_DATA.security_id
ACCOUNT.account_id < COMPLIANCE_VIOLATION.account_id
COMPLIANCE_RULE.rule_id < COMPLIANCE_VIOLATION.rule_id
BROKER.broker_id < TRADE.broker_id
TRADE.trade_id - SETTLEMENT.trade_id
ACCOUNT.account_id < SETTLEMENT.account_id 