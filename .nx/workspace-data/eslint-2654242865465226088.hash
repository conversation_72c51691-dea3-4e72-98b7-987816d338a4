{"4627095189323788750": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "747715242722797858": {"apps/angular-frontend-e2e": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend-e2e"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2417181500094607073": {}, "1536816600877973084": {".angular/cache/19.2.14/angular-frontend/vite/deps": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": ".angular/cache/19.2.14/angular-frontend/vite/deps"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7537446023531372630": {".angular/cache/19.2.14/angular-frontend/vite/deps_ssr": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": ".angular/cache/19.2.14/angular-frontend/vite/deps_ssr"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1054423603395147970": {}, "3332679611323428591": {}, "7240972479198302836": {}, "12968055282671116245": {}, "5444951189413066185": {}, "7848510571175896791": {}, "11732895458716869313": {}, "4153913132414750497": {}, "5964352539081103510": {}, "16814366922808384174": {}, "12992577525106223871": {}, "2904171426452249374": {}, "10342545888349508907": {}, "284394545894835822": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9029637647509853129": {}, "9695608506254465282": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "580500911594234756": {}, "5808826135073255333": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12839501693354417452": {}, "10405229181415634243": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13637341816731337114": {}, "1833353981174116549": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15880243608143064789": {}, "5816583457248472225": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2655361105043822986": {}, "13170143496837813650": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7528835422417077701": {}, "14042426650660437928": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "16874246849397347405": {}, "9774122102182568149": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14712129690311618044": {}, "4701488148560529478": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4287424258711655761": {}, "12992358401081365238": {}, "15725552312388097891": {}, "7631314206144155466": {}, "14217045096344672092": {}, "8044704269061606384": {}, "9118407545580082871": {}, "12188277833094160227": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17062897730471465750": {}, "13869342111841638037": {}, "17185616673157431957": {}, "1043529942519407151": {}, "1058299282756492601": {}, "11402169979635113580": {}, "12720577010842139675": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "3332763942439764995": {}, "1907661887068856289": {}, "14093933557728826766": {}, "7378339677090135129": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5219058110245023206": {}, "9748392768544432859": {}, "14183498854033603341": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11439174949767412755": {}, "14340657260767311117": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2294927018828708": {}, "2585623727217952486": {"uihub-core-main": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "uihub-core-main"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "10501339582565212240": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12339706098166703663": {}, "3002741097455117591": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6132340887855258909": {}, "17749934854327203447": {}, "13221017882541662653": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2692185474692316802": {}, "2931660349843895850": {}, "410597153250288824": {"icollect-web-main": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "icollect-web-main"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8655135651103298179": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5614103300864043497": {}, "11215836837328839407": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2219108829131605848": {}, "7411853852364029019": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7036815412113071380": {}, "8874949932264031997": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14033232211334648222": {}, "10567076619620786451": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "16640850018048493252": {}, "662218688827468280": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4503929837573374904": {}, "15719758895846265232": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7129610567064016449": {}, "15306314006200588143": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "747442855180711606": {}, "4100643044464938868": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1611845637662655737": {}, "11375685709909671923": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12736402977614229387": {}, "16726433124494170006": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13741486352509496869": {}, "4911543330985573163": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7333212131626113900": {}, "11282393212940036743": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6075520696522157542": {}, "17396834289806528361": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8278475659677822468": {}, "1781688930994085151": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14663595339978865935": {}, "5693530701876701416": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14425906669877279452": {}, "10637905793303256417": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14885174111774555644": {}, "14859600639213471430": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13065293380457754529": {}, "2252039664002776479": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14587582813204568399": {}, "16039860818606950394": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "895894536614225796": {}, "13746102523170341484": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15691745123182769762": {}, "8314522882040921393": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12480961527722982961": {}, "1426946134879302859": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1361795520808941388": {}, "1177979715223110112": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9391024915642940355": {}, "794833399616331523": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1996890902439892186": {}, "10043603745886887360": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17738283728535856826": {}, "5727275164931353694": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13376358060685260630": {}, "772637470272594379": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2331050416714330468": {}, "6599736000325156222": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "881613506233524915": {}, "14096134063827650584": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14145802078118213021": {}, "13526365621158731481": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8102799716983074587": {}, "17817801203181286596": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8551366821971075880": {}, "451257120691328618": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12970786423710794347": {}, "3160317271119527536": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "16665469995619563219": {}, "3179995749212859525": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8040245910188727368": {}, "13105647539055177398": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5775359867773861512": {}, "3467872307498816638": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "16527844826983421533": {}, "11573295124342469935": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5234405816801357624": {}, "6215897647041295651": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "10509456049268216380": {}, "4507126096162601186": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2941585903980105591": {}, "3578982622866048661": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6746780832162044813": {}, "1888856982349593029": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "125711143960629324": {}, "3249603211066937250": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12235941740658851319": {}, "4027588597736758028": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17334832433349811648": {}, "12147156475410231469": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7352839768866092708": {}, "17776216818936862824": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8140226852587087062": {}, "18261020315727908634": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8792280506073900783": {}, "6585691787826257478": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11288970452748317548": {}, "567465855414639704": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1776617064326593568": {}, "10310377939970476889": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15750646085045716066": {}, "11511837401038217330": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12805371499048281882": {}, "13299824220676140073": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2232826675365746250": {}, "4561316223153669878": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17796922315387912189": {}, "15144461382750032784": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6235123750477634936": {}, "18337970349266234357": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12902548155087555884": {}, "1342093068465231229": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6642167133154386954": {}, "2423342068768256742": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17486732626299031185": {}, "7754670088366369290": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1608300472078947415": {}, "10426058739246983301": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9548950356351352890": {}, "12386105566871870058": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4281574372080189542": {}, "124835925773276260": {}, "15259623671288153558": {"apps/angular-frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/angular-frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7421211565516153482": {}}