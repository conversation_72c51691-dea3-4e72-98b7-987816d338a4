{"version": "6.0", "nxVersion": "21.2.0-beta.2", "pathMappings": {}, "nxJsonPlugins": [{"name": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}, {"name": "@nx/eslint/plugin", "options": {"targetName": "lint"}}], "fileMap": {"nonProjectFiles": [], "projectFileMap": {"angular-frontend-e2e": [{"file": "apps/angular-frontend-e2e/eslint.config.mjs", "hash": "1172734138943286526", "deps": ["npm:eslint-plugin-playwright"]}, {"file": "apps/angular-frontend-e2e/playwright.config.ts", "hash": "3883159731593225931", "deps": ["npm:@playwright/test", "npm:@nx/playwright", "npm:@nx/devkit"]}, {"file": "apps/angular-frontend-e2e/project.json", "hash": "17887212465072532138"}, {"file": "apps/angular-frontend-e2e/src/example.spec.ts", "hash": "16019101014373124625", "deps": ["npm:@playwright/test"]}, {"file": "apps/angular-frontend-e2e/tsconfig.json", "hash": "18231654798499725131"}], "gp_brokerage_backoffice": [{"file": ".giti<PERSON>re", "hash": "6276891631813105442"}, {"file": ".prettieri<PERSON>re", "hash": "5407418692668764289"}, {"file": ".prettier<PERSON>", "hash": "16267754514737964994"}, {"file": "bbo-schema.txt", "hash": "276102020223692600"}, {"file": "eslint.config.mjs", "hash": "14755833654515998580", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "migrations.json", "hash": "14321910285289019118"}, {"file": "package-lock.json", "hash": "1370917753472540725"}, {"file": "package.json", "hash": "10242427692665441120", "deps": ["npm:@analogjs/vite-plugin-angular", "npm:@analogjs/vitest-angular", "npm:@angular-devkit/build-angular", "npm:@angular-devkit/core", "npm:@angular-devkit/schematics", "npm:@angular/build", "npm:@angular/cli", "npm:@angular/compiler-cli", "npm:@angular/language-service", "npm:@eslint/js", "npm:@nx/angular", "npm:@nx/devkit", "npm:@nx/eslint", "npm:@nx/eslint-plugin", "npm:@nx/js", "npm:@nx/playwright", "npm:@nx/vite", "npm:@nx/web", "npm:@playwright/test", "npm:@schematics/angular", "npm:@swc-node/register", "npm:@swc/core", "npm:@swc/helpers", "npm:@types/express", "npm:@types/node", "npm:@typescript-eslint/utils", "npm:@vitest/coverage-v8", "npm:@vitest/ui", "npm:angular-eslint", "npm:eslint", "npm:eslint-config-prettier", "npm:eslint-plugin-playwright", "npm:jiti", "npm:jsdom", "npm:prettier", "npm:tslib", "npm:typescript", "npm:typescript-eslint", "npm:vite", "npm:vitest", "npm:@angular/common", "npm:@angular/compiler", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/platform-browser", "npm:@angular/platform-browser-dynamic", "npm:@angular/platform-server", "npm:@angular/router", "npm:@angular/ssr", "npm:express", "npm:rxjs", "npm:zone.js"]}, {"file": "supabase/.branches/_current_branch", "hash": "5322843316632772325"}, {"file": "supabase/.temp/cli-latest", "hash": "4762769169091505645"}, {"file": "supabase/config.toml", "hash": "3494305167582112512"}, {"file": "supabase/migrations/20250605145505_initial_manual_schema_setup.sql", "hash": "16382692136414676909"}, {"file": "supabase/schema.sql", "hash": "16382692136414676909"}, {"file": "tsconfig.base.json", "hash": "16936441711144470533"}, {"file": "vitest.workspace.ts", "hash": "4665328684042819579"}], "angular-frontend": [{"file": "apps/angular-frontend/eslint.config.mjs", "hash": "16756400681144203361", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "apps/angular-frontend/project.json", "hash": "3117232837738887526"}, {"file": "apps/angular-frontend/src/app/app.component.css", "hash": "3244421341483603138"}, {"file": "apps/angular-frontend/src/app/app.component.html", "hash": "11500387839946465417"}, {"file": "apps/angular-frontend/src/app/app.component.spec.ts", "hash": "1890076627691762972", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/angular-frontend/src/app/app.component.ts", "hash": "13517611637142778362", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/angular-frontend/src/app/app.config.server.ts", "hash": "472700109066692288", "deps": ["npm:@angular/core", "npm:@angular/platform-server"]}, {"file": "apps/angular-frontend/src/app/app.config.ts", "hash": "1621826221232096503", "deps": ["npm:@angular/core", "npm:@angular/router", "npm:@angular/platform-browser"]}, {"file": "apps/angular-frontend/src/app/app.routes.ts", "hash": "13545874606124809888", "deps": ["npm:@angular/router"]}, {"file": "apps/angular-frontend/src/app/features/brokerage-onboarding.css", "hash": "17765442702328208676"}, {"file": "apps/angular-frontend/src/app/features/brokerage-onboarding.html", "hash": "15816848304177574097"}, {"file": "apps/angular-frontend/src/app/features/brokerage-onboarding.spec.ts", "hash": "1753374527580675844", "deps": ["npm:@angular/core"]}, {"file": "apps/angular-frontend/src/app/features/brokerage-onboarding.ts", "hash": "2126402878908267321", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms"]}, {"file": "apps/angular-frontend/src/app/nx-welcome.component.ts", "hash": "8411732640582258509", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "apps/angular-frontend/src/app/shared/components/sidebar/sidebar.component.css", "hash": "13403369194049809045"}, {"file": "apps/angular-frontend/src/app/shared/components/sidebar/sidebar.component.html", "hash": "2378276692782118048"}, {"file": "apps/angular-frontend/src/app/shared/components/sidebar/sidebar.component.ts", "hash": "12345776602218343442", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router"]}, {"file": "apps/angular-frontend/src/app/shared/components/steps/step.directive.ts", "hash": "8463745168922348493", "deps": ["npm:@angular/core"]}, {"file": "apps/angular-frontend/src/app/shared/components/steps/steps.component.html", "hash": "15073498873488701540"}, {"file": "apps/angular-frontend/src/app/shared/components/steps/steps.component.ts", "hash": "1451423501475707893", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "apps/angular-frontend/src/app/shared/components/topbar/topbar.component.css", "hash": "15197532146500358543"}, {"file": "apps/angular-frontend/src/app/shared/components/topbar/topbar.component.html", "hash": "15616188743096201788"}, {"file": "apps/angular-frontend/src/app/shared/components/topbar/topbar.component.ts", "hash": "12575595230345036322", "deps": ["npm:@angular/core"]}, {"file": "apps/angular-frontend/src/assets/fonts/Satoshi-Bold.ttf", "hash": "15220361477244295055"}, {"file": "apps/angular-frontend/src/assets/fonts/Satoshi-Medium.ttf", "hash": "4280300880547919822"}, {"file": "apps/angular-frontend/src/assets/fonts/Satoshi-Regular.ttf", "hash": "996530835745513683"}, {"file": "apps/angular-frontend/src/assets/fonts/fonts.css", "hash": "10320767092514487324"}, {"file": "apps/angular-frontend/src/assets/images/default-logo.png", "hash": "7318526223042627551"}, {"file": "apps/angular-frontend/src/assets/uihub-core/css/uihub-core.min.css", "hash": "13320793439728694926"}, {"file": "apps/angular-frontend/src/assets/uihub-core/js/uihub-core.min.js", "hash": "3040351475468278545"}, {"file": "apps/angular-frontend/src/assets/uihub-icons/outlined/outlined.umd.js", "hash": "11474730004270946329"}, {"file": "apps/angular-frontend/src/assets/uihub-icons/uihub-icons.min.css", "hash": "17966795615388944818"}, {"file": "apps/angular-frontend/src/assets/uihub-icons/uihub-icons.umd.js", "hash": "5704438075376433501"}, {"file": "apps/angular-frontend/src/assets/version.json", "hash": "16266726924253696700"}, {"file": "apps/angular-frontend/src/favicon.ico", "hash": "14352826501315855109"}, {"file": "apps/angular-frontend/src/index.html", "hash": "15510631310141088385"}, {"file": "apps/angular-frontend/src/main.server.ts", "hash": "11653607448136507287", "deps": ["npm:@angular/platform-browser"]}, {"file": "apps/angular-frontend/src/main.ts", "hash": "16635586463787978962", "deps": ["npm:@angular/platform-browser"]}, {"file": "apps/angular-frontend/src/server.ts", "hash": "3822669169679213965", "deps": ["npm:@angular/common", "npm:@angular/ssr", "npm:express"]}, {"file": "apps/angular-frontend/src/styles.css", "hash": "14400819857596146413"}, {"file": "apps/angular-frontend/src/test-setup.ts", "hash": "7283558780385135807", "deps": ["npm:@analogjs/vitest-angular", "npm:@angular/platform-browser-dynamic", "npm:@angular/core"]}, {"file": "apps/angular-frontend/tsconfig.app.json", "hash": "4112140274630772034"}, {"file": "apps/angular-frontend/tsconfig.editor.json", "hash": "8217547897093063137"}, {"file": "apps/angular-frontend/tsconfig.json", "hash": "6424220500541951295"}, {"file": "apps/angular-frontend/tsconfig.spec.json", "hash": "11359119564988205903"}, {"file": "apps/angular-frontend/vite.config.mts", "hash": "13317750787431048985", "deps": ["npm:vite", "npm:@analogjs/vite-plugin-angular", "npm:@nx/vite"]}]}}}