{"apps/angular-frontend-e2e": {"root": ["apps/angular-frontend-e2e/project.json", "nx/core/project-json"], "metadata.targetGroups": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "metadata.targetGroups.E2E (CI)": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "metadata.targetGroups.E2E (CI).0": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "metadata.targetGroups.E2E (CI).1": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.options": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.dependsOn": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.cache": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.inputs": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.outputs": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.executor": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.options.cwd": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.options.command": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.technologies": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.technologies.0": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.description": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.help": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.help.command": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.help.example": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.dependsOn": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.cache": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.inputs": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.outputs": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.executor": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options.cwd": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options.env": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options.command": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.technologies": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.technologies.0": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.description": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.help": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.help.command": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.help.example": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.executor": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.cache": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.inputs": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.outputs": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.dependsOn": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.technologies": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.technologies.0": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.description": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.nonAtomizedTarget": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.help": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.help.command": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.help.example": ["apps/angular-frontend-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.lint": ["apps/angular-frontend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.options": ["apps/angular-frontend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.outputs": ["apps/angular-frontend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["apps/angular-frontend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["apps/angular-frontend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["apps/angular-frontend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["apps/angular-frontend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["apps/angular-frontend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["apps/angular-frontend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["apps/angular-frontend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["apps/angular-frontend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["apps/angular-frontend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["apps/angular-frontend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "name": ["apps/angular-frontend-e2e/project.json", "nx/core/project-json"], "$schema": ["apps/angular-frontend-e2e/project.json", "nx/core/project-json"], "projectType": ["apps/angular-frontend-e2e/project.json", "nx/core/project-json"], "sourceRoot": ["apps/angular-frontend-e2e/project.json", "nx/core/project-json"], "// targets": ["apps/angular-frontend-e2e/project.json", "nx/core/project-json"], "implicitDependencies": ["apps/angular-frontend-e2e/project.json", "nx/core/project-json"], "implicitDependencies.angular-frontend": ["apps/angular-frontend-e2e/project.json", "nx/core/project-json"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/angular-frontend": {"root": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets": ["apps/angular-frontend/eslint.config.mjs", "@nx/eslint/plugin"], "name": ["apps/angular-frontend/project.json", "nx/core/project-json"], "$schema": ["apps/angular-frontend/project.json", "nx/core/project-json"], "projectType": ["apps/angular-frontend/project.json", "nx/core/project-json"], "prefix": ["apps/angular-frontend/project.json", "nx/core/project-json"], "sourceRoot": ["apps/angular-frontend/project.json", "nx/core/project-json"], "tags": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.executor": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.outputs": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.options": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.configurations": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.options.index": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.options.browser": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.options.polyfills": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.options.assets": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.options.styles": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.options.scripts": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.options.server": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.options.ssr": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.options.prerender": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.budgets": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.outputHashing": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.development.optimization": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.development.extractLicenses": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.development.sourceMap": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve.continuous": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve.executor": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve.defaultConfiguration": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.production": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.production.buildTarget": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.development": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.development.buildTarget": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.development.poll": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.extract-i18n": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.extract-i18n.executor": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.extract-i18n.options": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.extract-i18n.options.buildTarget": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.lint": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.lint.executor": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.test": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.test.executor": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.test.outputs": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.test.options": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.test.options.reportsDirectory": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve-static": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve-static.continuous": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve-static.executor": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve-static.options": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve-static.options.buildTarget": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve-static.options.port": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve-static.options.staticFilePath": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.serve-static.options.spa": ["apps/angular-frontend/project.json", "nx/core/project-json"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, ".": {"root": ["package.json", "nx/core/package-json"], "name": ["package.json", "nx/core/package-json"], "tags": ["package.json", "nx/core/package-json"], "tags.npm:public": ["package.json", "nx/core/package-json"], "metadata.targetGroups": ["package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["package.json", "nx/core/package-json"], "metadata.description": ["package.json", "nx/core/package-json"], "metadata.js": ["package.json", "nx/core/package-json"], "metadata.js.packageName": ["package.json", "nx/core/package-json"], "metadata.js.packageMain": ["package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["package.json", "nx/core/package-json"], "targets": ["package.json", "nx/core/package-json"], "targets.test": ["package.json", "nx/core/package-json"], "targets.test.executor": ["package.json", "nx/core/package-json"], "targets.test.options": ["package.json", "nx/core/package-json"], "targets.test.metadata": ["package.json", "nx/core/package-json"], "targets.test.options.script": ["package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["package.json", "nx/core/package-json"], "targets.nx-release-publish": ["package.json", "nx/core/package-json"], "targets.nx-release-publish.executor": ["package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["package.json", "nx/core/package-json"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}}