{"name": "gp_brokerage_backoffice", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@analogjs/vite-plugin-angular": "2.0.0-alpha.12", "@analogjs/vitest-angular": "2.0.0-alpha.12", "@angular-devkit/build-angular": "20.0.0-rc.3", "@angular-devkit/core": "20.0.0-rc.3", "@angular-devkit/schematics": "20.0.0-rc.3", "@angular/build": "20.0.0-rc.3", "@angular/cli": "20.0.0-rc.3", "@angular/compiler-cli": "20.0.0-rc.2", "@angular/language-service": "20.0.0-rc.2", "@eslint/js": "^9.8.0", "@nx/angular": "21.2.0-beta.2", "@nx/devkit": "21.2.0-beta.2", "@nx/eslint": "21.2.0-beta.2", "@nx/eslint-plugin": "21.2.0-beta.2", "@nx/js": "21.2.0-beta.2", "@nx/playwright": "21.2.0-beta.2", "@nx/vite": "21.2.0-beta.2", "@nx/web": "21.2.0-beta.2", "@playwright/test": "^1.36.0", "@schematics/angular": "20.0.0-rc.3", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/express": "^4.17.21", "@types/node": "18.16.9", "@typescript-eslint/utils": "^8.19.0", "@vitest/coverage-v8": "^3.0.5", "@vitest/ui": "^3.0.0", "angular-eslint": "^19.2.0", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-playwright": "^1.6.2", "jiti": "2.4.2", "jsdom": "~22.1.0", "prettier": "^2.6.2", "tslib": "^2.3.0", "typescript": "5.8.3", "typescript-eslint": "^8.19.0", "vite": "^6.0.0", "vitest": "^3.0.0"}, "nx": {}, "dependencies": {"@angular/common": "20.0.0-rc.2", "@angular/compiler": "20.0.0-rc.2", "@angular/core": "20.0.0-rc.2", "@angular/forms": "20.0.0-rc.2", "@angular/platform-browser": "20.0.0-rc.2", "@angular/platform-browser-dynamic": "20.0.0-rc.2", "@angular/platform-server": "20.0.0-rc.2", "@angular/router": "20.0.0-rc.2", "@angular/ssr": "20.0.0-rc.3", "express": "^4.21.2", "rxjs": "~7.8.0", "zone.js": "~0.15.0"}}